package core

import (
	"airAi/global"
	"airAi/initialize"
	"airAi/service/system"
	"fmt"

	"go.uber.org/zap"
)

type server interface {
	ListenAndServe() error
}

func RunWindowsServer() {
	// 从db加载jwt数据
	if global.GVA_DB != nil {
		system.LoadAll()
	}
	Router := initialize.Routers()
	Router.Static("/form-generator", "./resource/page")

	address := fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr)
	s := initServer(address, Router)

	global.GVA_LOG.Info("服务器启动成功", zap.String("address", address))

	if err := s.ListenAndServe(); err != nil {
		global.GVA_LOG.Error("服务器启动失败", zap.Error(err))
	}
}
