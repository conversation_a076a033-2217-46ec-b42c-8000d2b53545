package ear_ai

import (
	"airAi/middleware"

	"github.com/gin-gonic/gin"
)

type EarRouter struct{}

func (e *EarRouter) InitEarRouter(Router *gin.RouterGroup) {
	earRouter := Router.Group("/v1").Use(middleware.OperationRecord())
	{
		earRouter.POST("/login", airportApi.Login)                   // 登陆
		earRouter.POST("/forgotPassword", airportApi.ForgotPassword) // 忘记密码
		earRouter.POST("/googleLogin", airportApi.GoogleLogin)       // google登陆
		earRouter.POST("/register", airportApi.Register)             // 注册
		earRouter.POST("/bindPhone", airportApi.BindPhone)           // 绑定手机号
		earRouter.POST("/getCode", airportApi.SmCode)                // 获取验证码

	}
}

func (e *EarRouter) InitEarAuthorRouter(Router *gin.RouterGroup) {
	earRouter := Router.Group("v1").Use(middleware.JWTAuth())
	//earRouter := Router.Group("v1")
	{
		// 需要配额验证的API端点
		earRouter.POST("/recognize", airportApi.Recognize) // 语音识别
		earRouter.GET("/chat", airportApi.Chat)            // WebSocket聊天
		earRouter.POST("/tts", airportApi.TextToSpeech)    // 文字转语音
		earRouter.POST("/syncChat", airportApi.SyncChat)   // 同步聊天
		//earRouter.POST("/info", airportApi.GetQuotaInfo)   // 获取配额信息

		//// 配额管理API端点
		//quotaGroup := earRouter.Group("/quota").Use(middleware.JWTAuth())
		//{
		//	quotaGroup.GET("/info", airportApi.GetQuotaInfo)                   // 获取配额信息
		//	quotaGroup.POST("/purchase", airportApi.PurchaseExtraQuota)        // 购买额外配额
		//	quotaGroup.POST("/subscribe", airportApi.SubscribeMonthly)         // 订阅月度服务
		//	quotaGroup.GET("/purchase/history", airportApi.GetPurchaseHistory) // 获取购买历史
		//	quotaGroup.GET("/usage/history", airportApi.GetUsageHistory)       // 获取使用历史
		//
		//	// 管理员API端点
		//	quotaGroup.POST("/admin/reset", airportApi.ForceResetQuota)        // 强制重置配额
		//	quotaGroup.GET("/admin/statistics", airportApi.GetQuotaStatistics) // 获取统计信息
		//}

		// 不需要配额验证的API端点
		earRouter.POST("/updateUser", middleware.JWTAuth(), airportApi.UpdateUser) // 修改用户信息
	}
}
