package airport

import (
	"airAi/common/requst"
	"airAi/global"
	"airAi/utils"
	"context"
	"errors"
	"model/airpods"
	"model/system/request"

	"github.com/gofrs/uuid/v5"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

func (s *UserService) Login(req requst.LoginRequest) (airUser *airpods.AirpodsUser, err error) {
	var user airpods.AirpodsUser
	if utils.PhoneValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", req.Account).First(&user).Error
		if err != nil {
			return nil, err
		}
	}

	if utils.EmailValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("email =?", req.Account).First(&user).Error
		if err != nil {
			return nil, err
		}
	}
	if req.Code != "" {
		// 检查Redis是否可用
		if global.GVA_REDIS == nil {
			return nil, errors.New("Redis服务不可用，无法验证验证码")
		}

		codeStr, err := global.GVA_REDIS.Get(context.Background(), req.Account).Result()
		if err != nil && err != redis.Nil {
			return nil, err
		}
		if codeStr != req.Code {
			return nil, errors.New("invalid code")
		}
	} else {
		if req.Password == "" {
			return nil, errors.New("invalid password")
		}
		if ok := utils.BcryptCheck(req.Password, user.Password); !ok {
			return nil, errors.New("invalid password")
		}
	}

	return &user, nil
}

func (s *UserService) Register(req request.Register) (airUser *airpods.AirpodsUser, err error) {
	if req.Password != req.ConfirmPassword {
		return nil, errors.New("Confirm passwords are not equal ")
	}
	var user airpods.AirpodsUser
	if utils.PhoneValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", req.Account).First(&user).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		user.Phone = req.Account
	}

	if utils.EmailValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("email =?", req.Account).First(&user).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		user.Email = req.Account
	}
	if user.ID > 0 {
		return nil, errors.New("account exist")
	}
	// 检查Redis是否可用
	if global.GVA_REDIS == nil {
		return nil, errors.New("Redis服务不可用，无法验证验证码")
	}

	codeStr, err := global.GVA_REDIS.Get(context.Background(), req.Account).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	if codeStr != req.Code {
		return nil, errors.New("invalid code")
	}
	user.UUID, _ = uuid.NewV4()
	user.Password = utils.BcryptHash(req.Password)
	global.GVA_DB.Create(&user)
	return &user, nil
}

func (s *UserService) LogOut() {

}

func (s *UserService) BindPhone(uid int64, phone string) error {
	var user airpods.AirpodsUser
	err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("uid=?", uid).First(&user).Error
	if err != nil {
		return err
	}
	global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", phone).First(&user)
	if user.ID > 0 {
		return errors.New("phone exist")
	}
	if user.Phone != "" {
		return errors.New("It's already bound")
	}
	return global.GVA_DB.Model(airpods.AirpodsUser{}).Where("uid=?", uid).Update("phone", phone).Error
}

func (s *UserService) UpdateUser(user airpods.AirpodsUser) error {
	return global.GVA_DB.Model(airpods.AirpodsUser{}).Updates(&user).Error
}

func (s *UserService) ForgotPassword(req requst.LoginRequest) (airUser *airpods.AirpodsUser, err error) {
	if req.Password == "" {
		return nil, errors.New("invalid password")
	}
	if req.Code != "" {
		return nil, errors.New("code not exist")
	}
	var user airpods.AirpodsUser
	if utils.PhoneValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", req.Account).First(&user).Error
		if err != nil {
			return nil, err
		}
	}

	if utils.EmailValidation(req.Account) {
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("email =?", req.Account).First(&user).Error
		if err != nil {
			return nil, err
		}
	}

	// 检查Redis是否可用
	if global.GVA_REDIS == nil {
		return nil, errors.New("Redis服务不可用，无法验证验证码")
	}

	codeStr, err := global.GVA_REDIS.Get(context.Background(), req.Account).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	if codeStr != req.Code {
		return nil, errors.New("invalid code")
	}
	user.Password = utils.BcryptHash(req.Password)
	err = global.GVA_DB.Save(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}
