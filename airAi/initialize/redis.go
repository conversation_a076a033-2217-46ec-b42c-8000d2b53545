package initialize

import (
	"context"

	"github.com/redis/go-redis/v9"

	"airAi/global"

	"go.uber.org/zap"
)

func Redis() {
	redisCfg := global.GVA_CONFIG.Redis
	var client redis.UniversalClient

	// 使用集群模式
	if redisCfg.UseCluster {
		client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:    redisCfg.ClusterAddrs,
			Password: redisCfg.Password,
		})
	} else {
		// 使用单例模式
		client = redis.NewClient(&redis.Options{
			Addr:     redisCfg.Addr,
			Password: redisCfg.Password,
			DB:       redisCfg.DB,
		})
	}

	// 尝试连接Redis并进行ping测试
	pong, err := client.Ping(context.Background()).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis连接失败，应用将在没有Redis的情况下继续运行",
			zap.Error(err),
			zap.String("redis_addr", redisCfg.Addr),
			zap.Bool("use_cluster", redisCfg.UseCluster))

		// 不设置全局Redis客户端，让应用在没有Redis的情况下继续运行
		global.GVA_REDIS = nil

		global.GVA_LOG.Warn("Redis服务不可用，某些功能可能受到影响，但应用将继续运行")
	} else {
		global.GVA_LOG.Info("Redis连接成功",
			zap.String("pong", pong),
			zap.String("redis_addr", redisCfg.Addr),
			zap.Bool("use_cluster", redisCfg.UseCluster))
		global.GVA_REDIS = client
	}
}
