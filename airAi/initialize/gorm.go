package initialize

import (
	"fmt"
	"model/airpods"
	"model/system"
	"strings"

	"airAi/global"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "mysql":
		return GormMysql()
	case "pgsql":
		return GormPgSql()
	case "oracle":
		return GormOracle()
	case "mssql":
		return GormMssql()
	case "sqlite":
		return GormSqlite()
	default:
		return GormMysql()
	}
}

func RegisterTables() {
	db := global.GVA_DB

	// 定义需要迁移的表结构
	tables := []any{
		airpods.AirpodsUser{},
		airpods.Quota{},
		airpods.PurchaseRecord{},
		airpods.QuotaUsageLog{},
		system.SysOperationRecord{},
		airpods.JwtBlacklist{},
	}

	// 逐个迁移表，提供更详细的错误处理
	for _, table := range tables {
		err := db.AutoMigrate(table)
		if err != nil {
			// 检查是否是重复键错误
			errStr := err.Error()
			if strings.Contains(errStr, "Duplicate key name") ||
				strings.Contains(errStr, "duplicate key") ||
				strings.Contains(errStr, "already exists") {
				global.GVA_LOG.Warn("表结构迁移遇到重复键，继续执行",
					zap.String("table", fmt.Sprintf("%T", table)),
					zap.Error(err))
				continue
			}

			// 其他类型的错误，记录但不退出程序
			global.GVA_LOG.Error("表结构迁移失败",
				zap.String("table", fmt.Sprintf("%T", table)),
				zap.Error(err))

			// 不再使用os.Exit(0)强制退出，而是记录错误并继续
			global.GVA_LOG.Warn("表结构迁移失败，但应用将继续运行，某些功能可能受到影响")
		} else {
			global.GVA_LOG.Debug("表结构迁移成功",
				zap.String("table", fmt.Sprintf("%T", table)))
		}
	}

	global.GVA_LOG.Info("数据库表注册完成")
}
