package initialize

import (
	"github.com/songzhibin97/gkit/cache/local_cache"

	"airAi/global"
	"airAi/utils"
)

func OtherInit() {
	// 解析JWT过期时间配置
	dr, err := utils.ParseDuration(global.GVA_CONFIG.JWT.ExpiresTime)
	if err != nil {
		panic(err)
	}

	// 解析JWT缓冲时间配置
	_, err = utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)
	if err != nil {
		panic(err)
	}

	// 初始化JWT黑名单缓存
	global.BlackCache = local_cache.NewCache(
		local_cache.SetDefaultExpire(dr),
	)
}
