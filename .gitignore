# IDE and Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.iml

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local env files
.env
.env.local
.env.*.local

# Go build artifacts and binaries
airAi
airAi.exe
ariport
ariport.exe
*.exe
*.exe~
*.dll
*.so
*.dylib

# Go test binary, built with `go test -c`
*.test

# Go coverage files
*.out
*.prof

# Build directories
build/
dist/
bin/
target/

# Dependency directories
vendor/

# Go module download cache
go.sum

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Log files
log/
logs/
*.log

# Upload directories
server/uploads/
uploads/

# Audio files
*.mp3
*.wav
*.m4a

# Archive files
*.zip
*.tar.gz
*.rar


